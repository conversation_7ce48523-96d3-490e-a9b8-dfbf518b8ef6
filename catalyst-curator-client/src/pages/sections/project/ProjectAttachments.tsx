import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Group } from '../../../lib/ui/atoms/Group';
import { Label } from '../../../lib';
import { AttachmentLocals } from '../opportunity/curated-opportunity-groups/OpportunityAttachments';
import { AttachmentInfo, FileAttachments } from '../../../lib/ui/molecules/FileAttachments';
import { ProjectStore } from '../../../stores/ProjectStore';
import { openWebFileLink } from '../../../utilities/File';
import { Attachment, Location } from '../../../services/codegen/types';
import { Links } from '../opportunity/Links';
import { UserStore } from '../../../stores';
import { AttachmentDialog } from '../opportunity/AttachmentDialog';

interface ProjectAttachmentsProps {
  theme: ReactNativePaper.ThemeProp;
  projectStore: ProjectStore;
  userStore: UserStore;
}

export const ProjectAttachments = withTheme(
  observer(({ theme, projectStore, userStore }: ProjectAttachmentsProps) => {
    const {
      colors,
      styles: { margins },
    } = theme;

    const locals = useLocalObservable<AttachmentLocals>(() => ({
      displayName: '',
      notes: '',
      fileName: '',
      mimetype: '',
      isDialogOpen: false,
      editingAttachmentId: null,
      pendingFile: null,
      pendingUri: '',
      setDisplayName(displayName: string) {
        this.displayName = displayName;
      },
      setNotes(notes: string) {
        this.notes = notes;
      },
      setFileName(fileName: string) {
        this.fileName = fileName;
      },
      setMimetype(mimetype: string) {
        this.mimetype = mimetype;
      },
      setIsDialogOpen(value: boolean) {
        this.isDialogOpen = value;
      },
      setEditingAttachmentId(id: string | null) {
        this.editingAttachmentId = id;
      },
      setPendingFile(file: File | null) {
        this.pendingFile = file;
      },
      setPendingUri(uri: string) {
        this.pendingUri = uri;
      },
    }));

    function resetLocals() {
      locals.setIsDialogOpen(false);
      locals.setEditingAttachmentId(null);
      locals.setDisplayName('');
      locals.setNotes('');
      locals.setFileName('');
      locals.setMimetype('');
      locals.setPendingFile(null);
      locals.setPendingUri('');
    }

    return (
      <Group title="Attachments" style={{ zIndex: 1 }}>
        <Label textStyle={{ color: colors.secondaryTextColor }}>Files</Label>
        <FileAttachments
          style={[margins.BottomL, { marginTop: 8 }]}
          getAttachments={() => projectStore.project?.attachments as AttachmentInfo[]}
          onPress={(id) => handleOnRetrieveDocument(projectStore, id)}
          onDelete={(id) => handleOnDeleteDocument(projectStore, id)}
          getIsInProgress={() => projectStore.fileUploadInProgress}
          onAddAttachment={async (result) => await handleOnAddDocument(result, projectStore)}
          onEditAttachment={(attachmentId) => {
            const attachment = projectStore.project?.attachments.find((a: Attachment) => a.id === attachmentId);
            if (attachment) {
              locals.setFileName(attachment.name);
              locals.setDisplayName(attachment.displayName || '');
              locals.setNotes(attachment.notes || '');
              locals.setMimetype(attachment.mimetype || '');
              locals.setEditingAttachmentId(attachmentId);
              locals.setIsDialogOpen(true);
            }
          }}
          onShowAttachmentDialog={(file, uri) => {
            locals.setPendingFile(file);
            locals.setPendingUri(uri);
            locals.setFileName(file.name);
            locals.setMimetype(file.type);
            locals.setDisplayName('');
            locals.setNotes('');
            locals.setEditingAttachmentId(null);
            locals.setIsDialogOpen(true);
          }}
          getEditable={() => true}
          iconSize={32}
        />
        <Label textStyle={{ color: colors.secondaryTextColor, marginBottom: 8 }}>Links</Label>
        <Links
          onAddLink={(url, name, notes) => handleOnAddLink(projectStore, url, name, notes)}
          onDeleteLink={(id) => handleOnDeleteLink(projectStore, id)}
          onUpdateLink={(id, url, name, notes) => handleOnUpdateLink(projectStore, id, url, name, notes)}
          editable={true}
          links={projectStore.project?.links || []}
          userStore={userStore}
        />
        <AttachmentDialog
          getVisible={() => locals.isDialogOpen}
          onConfirm={async () => {
            if (locals.editingAttachmentId) {
              await projectStore.updateAttachment(
                locals.editingAttachmentId,
                locals.displayName || undefined,
                locals.notes || undefined,
              );
            } else if (locals.pendingFile && locals.pendingUri) {
              await projectStore.addAttachment({
                file: locals.pendingFile,
                uri: locals.pendingUri,
                displayName: locals.displayName || undefined,
                notes: locals.notes || undefined,
              });
            }
            resetLocals();
          }}
          onDelete={async () => {
            if (locals.editingAttachmentId) {
              await projectStore.deleteAttachment(locals.editingAttachmentId);
              resetLocals();
            }
          }}
          onDismiss={() => {
            resetLocals();
          }}
          locals={locals}
          userStore={userStore}
        />
      </Group>
    );
  }),
);

async function handleOnAddLink(projectStore: ProjectStore, url: string, name: string, notes: string) {
  await projectStore.addLink(url, name, notes);
}

async function handleOnUpdateLink(
  projectStore: ProjectStore,
  linkId: string,
  url: string,
  name: string,
  notes: string,
) {
  await projectStore.updateLink(linkId, url, name, notes);
}

async function handleOnDeleteLink(projectStore: ProjectStore, linkId: string) {
  await projectStore.deleteLink(linkId);
}

const handleOnAddDocument = (result: { file: File; uri: string }, projectStore: ProjectStore): void => {
  projectStore.addAttachment(result);
};

const handleOnDeleteDocument = (projectStore: ProjectStore, attachmentId: string): void => {
  projectStore.deleteAttachment(attachmentId);
};

const handleOnRetrieveDocument = (projectStore: ProjectStore, attachmentId: string): void => {
  const attachment = projectStore.project?.attachments.find((a: Attachment) => a.id === attachmentId);
  projectStore.getAttachment(attachmentId).then((loc: Location) => {
    return openWebFileLink(loc.location, attachment?.name || '', attachment?.mimetype || '');
  });
};
