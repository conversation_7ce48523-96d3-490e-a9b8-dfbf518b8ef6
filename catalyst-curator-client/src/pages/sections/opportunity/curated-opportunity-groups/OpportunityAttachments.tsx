import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Group } from '../../../../lib/ui/atoms/Group';
import { CuratedOpportunityGroupProps, LabeledTextInputProps } from '../CuratedOpportunity';
import { CollapsibleView } from '../../../../lib/ui/atoms/CollapsibleView';
import { Text } from '../../../../lib/ui/atoms/Text';
import { OpportunityStore, UserStore } from '../../../../stores';
import { AttachmentInfo, FileAttachments } from '../../../../lib/ui/molecules/FileAttachments';
import { Attachment, Location } from '../../../../services/codegen/types';
import { openWebFileLink } from '../../../../utilities/File';
import { Label } from '../../../../lib';
import { Links } from '../Links';
import { AttachmentDialog } from '../AttachmentDialog';

export type AttachmentLocals = {
  displayName: string;
  notes: string;
  fileName: string;
  mimetype?: string;
  isDialogOpen: boolean;
  editingAttachmentId: string | null;
  setDisplayName(displayName: string): void;
  setNotes(notes: string): void;
  setFileName(fileName: string): void;
  setMimetype(mimetype: string): void;
  setIsDialogOpen(value: boolean): void;
  setEditingAttachmentId(id: string | null): void;
  setPendingFile(file: File | null): void;
  setPendingUri(uri: string): void;
  pendingFile: File | null;
  pendingUri: string;
};

interface OpportunityAttachmentsProps extends CuratedOpportunityGroupProps {
  getLabeledTextInput: (props: LabeledTextInputProps) => JSX.Element;
  userStore: UserStore;
}

export const OpportunityAttachments = withTheme(
  observer(
    ({ editable, theme, opportunityStore, label, getLabeledTextInput, userStore }: OpportunityAttachmentsProps) => {
      const {
        styles: { components, margins, fontSizes, fonts },
        colors,
      } = theme;

      const locals = useLocalObservable<AttachmentLocals>(() => ({
        displayName: '',
        notes: '',
        fileName: '',
        mimetype: '',
        isDialogOpen: false,
        editingAttachmentId: null,
        pendingFile: null,
        pendingUri: '',
        setDisplayName(displayName: string) {
          this.displayName = displayName;
        },
        setNotes(notes: string) {
          this.notes = notes;
        },
        setFileName(fileName: string) {
          this.fileName = fileName;
        },
        setMimetype(mimetype: string) {
          this.mimetype = mimetype;
        },
        setIsDialogOpen(value: boolean) {
          this.isDialogOpen = value;
        },
        setEditingAttachmentId(id: string | null) {
          this.editingAttachmentId = id;
        },
        setPendingFile(file: File | null) {
          this.pendingFile = file;
        },
        setPendingUri(uri: string) {
          this.pendingUri = uri;
        },
      }));

      function resetLocals() {
        locals.setIsDialogOpen(false);
        locals.setEditingAttachmentId(null);
        locals.setDisplayName('');
        locals.setNotes('');
        locals.setFileName('');
        locals.setMimetype('');
        locals.setPendingFile(null);
        locals.setPendingUri('');
      }
      return (
        <Group title="Attachments" style={[{ zIndex: 2 }]} description={label}>
          <Label textStyle={{ color: colors.secondaryTextColor }}>Files</Label>
          <FileAttachments
            style={[margins.BottomL, { marginTop: 8 }]}
            getAttachments={() => opportunityStore.opportunity?.attachments as AttachmentInfo[]}
            onPress={(id) => handleOnRetrieveDocument(opportunityStore, id)}
            onDelete={(id) => handleOnDeleteDocument(opportunityStore, id)}
            getIsInProgress={() => opportunityStore.fileUploadInProgress}
            onAddAttachment={async (result) => await handleOnAddDocument(result, opportunityStore)}
            onEditAttachment={(attachmentId) => {
              const attachment = opportunityStore.opportunity?.attachments.find(
                (a: Attachment) => a.id === attachmentId,
              );
              if (attachment) {
                locals.setFileName(attachment.name);
                locals.setDisplayName(attachment.displayName || '');
                locals.setNotes(attachment.notes || '');
                locals.setMimetype(attachment.mimetype || '');
                locals.setEditingAttachmentId(attachmentId);
                locals.setIsDialogOpen(true);
              }
            }}
            onShowAttachmentDialog={(file, uri) => {
              locals.setPendingFile(file);
              locals.setPendingUri(uri);
              locals.setFileName(file.name);
              locals.setDisplayName('');
              locals.setNotes('');
              locals.setMimetype(file.type);
              locals.setEditingAttachmentId(null);
              locals.setIsDialogOpen(true);
            }}
            getEditable={() => editable}
            iconSize={32}
          />
          <Label textStyle={{ color: colors.secondaryTextColor, marginBottom: 8 }}>Links</Label>
          <Links
            onAddLink={(url, name, notes) => handleOnAddLink(opportunityStore, url, name, notes)}
            onDeleteLink={(id) => handleOnDeleteLink(opportunityStore, id)}
            onUpdateLink={(id, url, name, notes) => handleOnUpdateLink(opportunityStore, id, url, name, notes)}
            links={opportunityStore.opportunity?.links || []}
            editable={editable}
            userStore={userStore}
          />
          {
            // show in readonly mode if it's not empty
            !editable &&
              opportunityStore.getValue('attachmentNotes') &&
              getLabeledTextInput({
                opportunityStore,
                fieldName: 'attachmentNotes',
                labelText: 'Notes About Attached Files',
                textInputProps: {
                  multiline: true,
                  placeholder: 'Additional information about attached files',
                },
                editable,
                style: [components.rowStyle],
              })
          }
          {
            // show in edit mode, expanded if data present
            editable && (
              <CollapsibleView
                header={
                  <Text style={[fontSizes.mediumSmall, fonts.medium, { color: colors.primary }]}>ATTACHMENT NOTES</Text>
                }
                contentStyle={[margins.VerticalM]}
                defaultOpen={!!opportunityStore.getValue('attachmentNotes')}
              >
                {getLabeledTextInput({
                  opportunityStore,
                  fieldName: 'attachmentNotes',
                  labelText: 'Notes About Attached Files',
                  textInputProps: {
                    multiline: true,
                    placeholder: 'Additional information about attached files',
                  },
                  editable,
                  style: [components.rowStyle],
                })}
              </CollapsibleView>
            )
          }
          <AttachmentDialog
            getVisible={() => locals.isDialogOpen}
            onConfirm={async () => {
              if (locals.editingAttachmentId) {
                await opportunityStore.updateAttachment(
                  locals.editingAttachmentId,
                  locals.displayName || undefined,
                  locals.notes || undefined,
                );
              } else if (locals.pendingFile && locals.pendingUri) {
                await opportunityStore.addAttachment({
                  file: locals.pendingFile,
                  uri: locals.pendingUri,
                  displayName: locals.displayName || undefined,
                  notes: locals.notes || undefined,
                });
              }
              resetLocals();
            }}
            onDelete={async () => {
              if (locals.editingAttachmentId) {
                await opportunityStore.deleteAttachment(locals.editingAttachmentId);
                resetLocals();
              }
            }}
            onDismiss={() => {
              resetLocals();
            }}
            locals={locals}
            userStore={userStore}
          />
        </Group>
      );
    },
  ),
);

async function handleOnDeleteLink(opportunityStore: OpportunityStore, linkId: string) {
  opportunityStore.deleteLink(linkId);
}

async function handleOnAddLink(opportunityStore: OpportunityStore, url: string, name: string, notes: string) {
  await opportunityStore.addLink(url, name, notes);
}

async function handleOnUpdateLink(
  opportunityStore: OpportunityStore,
  linkId: string,
  url: string,
  name: string,
  notes: string,
) {
  await opportunityStore.updateLink(linkId, url, name, notes);
}

const handleOnRetrieveDocument = (opportunityStore: OpportunityStore, attachmentId: string): void => {
  const attachment = opportunityStore.opportunity?.attachments.find((a: Attachment) => a.id === attachmentId);
  opportunityStore.getAttachment(attachmentId).then((loc: Location) => {
    return openWebFileLink(loc.location, attachment?.name || '', attachment?.mimetype || '');
  });
};

const handleOnAddDocument = async (
  result: { file: File; uri: string },
  opportunityStore: OpportunityStore,
): Promise<void> => {
  await opportunityStore.addAttachment(result);
};

const handleOnDeleteDocument = (opportunityStore: OpportunityStore, attachmentId: string): void => {
  opportunityStore.deleteAttachment(attachmentId);
};
