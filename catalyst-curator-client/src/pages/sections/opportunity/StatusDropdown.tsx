import { withTheme } from 'react-native-paper';
import { LabeledDependentDropdown } from '../../../lib/ui/molecules/LabeledDependentDropdown';
import { OpportunityStore } from '../../../stores';
import { observer } from 'mobx-react';
import { OpportunityStatus } from '../../../services/codegen/types';
import { MenuGroup, MenuItem } from '../../../lib';

export const StatusItems: MenuGroup[] = [
  { item: { label: 'In Curation', value: OpportunityStatus.InCuration } },
  { item: { label: 'On Hold', value: OpportunityStatus.OnHold } },
  {
    item: {
      label: 'Closed',
      value: 'Closed',
    },
    children: [
      {
        item: { label: 'Resolved', value: OpportunityStatus.Resolved },
        children: [
          { item: { label: 'Existing Solution', value: 'Existing Solution' } },
          { item: { label: 'Solution Developed', value: 'Solution Developed' } },
        ],
      },
      {
        item: { label: 'Not Selected', value: OpportunityStatus.NotSelected },
        children: [
          { item: { label: 'Not Selected - No Subcategory', value: 'Not Selected - No Subcategory' } },
          { item: { label: 'Technology Not Viable', value: 'Technology Not Viable' } },
          { item: { label: 'Command Priorities', value: 'Command Priorities' } },
          { item: { label: 'Resource Constraints: Funding', value: 'Resource Constraints: Funding' } },
          { item: { label: 'Resource Constraints: Personnel', value: 'Resource Constraints: Personnel' } },
          { item: { label: 'Resource Constraints: Materiel', value: 'Resource Constraints: Materiel' } },
          { item: { label: 'Regulatory Constraint', value: 'Regulatory Constraint' } },
        ],
      },
      { item: { label: 'Duplicate', value: OpportunityStatus.Duplicate } },
    ],
  },
];

interface StatusDropdownProps {
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
  isOpportunityArchived: boolean;
}
export const StatusDropdown = withTheme(
  observer(({ editable, handleDebounce, opportunityStore, theme, isOpportunityArchived }: StatusDropdownProps) => {
    function getDefaultStatus() {
      const status = opportunityStore.opportunity?.status;
      const statusReason = opportunityStore.opportunity?.statusReason;
      if (
        status === OpportunityStatus.Resolved ||
        status === OpportunityStatus.NotSelected ||
        status === OpportunityStatus.Duplicate
      ) {
        return [
          {
            fieldName: 'statusLevel1',
            label: 'Opportunity Status',
            value: {
              label: 'Closed',
              value: 'Closed',
            },
          },
          {
            fieldName: 'statusLevel2',
            label: 'Opportunity Status',
            value: {
              label: status || 'Unassigned',
              value: status || 'Unassigned',
            },
          },
          {
            fieldName: 'statusLevel3',
            label: 'Reason',
            value: {
              label: statusReason || 'Unassigned',
              value: statusReason || 'Unassigned',
            },
          },
        ];
      }
      return [
        {
          fieldName: 'statusLevel1',
          label: 'Opportunity Status',
          value: {
            label: status || 'In Curation',
            value: status || 'In Curation',
          },
        },
        {
          fieldName: 'statusLevel2',
          label: 'Opportunity Status',
          value: {
            label: status || 'Unassigned',
            value: status || 'Unassigned',
          },
        },
        {
          fieldName: 'statusLevel3',
          label: 'Reason',
          value: {
            label: statusReason || 'Unassigned',
            value: statusReason || 'Unassigned',
          },
        },
      ];
    }

    function onItemSelected(item: MenuItem | undefined, fieldName: string) {
      if (fieldName === 'statusLevel1' && item?.value !== 'Closed') {
        opportunityStore.setValue('status', item?.value);
        handleDebounce(opportunityStore);
      }
      if (fieldName === 'statusLevel2') {
        opportunityStore.setValue('status', item?.value);
        handleDebounce(opportunityStore);
      }
      if (fieldName === 'statusLevel3') {
        opportunityStore.setValue('statusReason', item?.value);
        handleDebounce(opportunityStore);
      }
    }
    return (
      <LabeledDependentDropdown
        labelText="Status"
        dropdownMenuProps={{
          getMenuGroups: () => {
            return StatusItems;
          },
          defaultValues: getDefaultStatus(),
          onItemSelected: onItemSelected,
          getEditable: () => editable && !isOpportunityArchived,
          theme,
        }}
      />
    );
  }),
);
