import { withTheme } from 'react-native-paper';
import { Label } from '../lib';
import { Links } from '../pages/sections/opportunity/Links';
import { Link } from '../services/codegen/types';
import { UserStore } from '../stores';

interface LinksSectionProps {
  theme: ReactNativePaper.ThemeProp;
  userStore: UserStore;
  editable?: boolean;
  links: Link[];
  onAddLink: (url: string, name: string, notes: string) => void;
  onDeleteLink: (id: string) => void;
  onUpdateLink: (id: string, url: string, name: string, notes: string) => void;
}

export const LinksSection = withTheme(
  ({ theme, userStore, editable = true, links, onAddLink, onDeleteLink, onUpdateLink }: LinksSectionProps) => {
    const { colors } = theme;

    return (
      <>
        <Label textStyle={{ color: colors.secondaryTextColor, marginBottom: 8 }}>Links</Label>
        <Links
          onAddLink={onAddLink}
          onDeleteLink={onDeleteLink}
          onUpdateLink={onUpdateLink}
          links={links}
          editable={editable}
          userStore={userStore}
        />
      </>
    );
  },
);
